<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <el-row justify="end">
          <el-col :span="24" style="text-align: right;">
            <el-dropdown @command="handleDropdownCommand">
              <el-button type="primary" icon="el-icon-plus">
                新建任务<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="batchAdd">批量新增</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-col>
        </el-row>
        <el-row :gutter="20" align="middle" style="margin-top: 18px;">
          <el-col :span="5">
            <el-radio-group v-model="filterForm.taskType" @change="handleFilter">
              <el-radio-button label="all">全部任务</el-radio-button>
              <el-radio-button label="assigned">指派给我的</el-radio-button>
              <el-radio-button label="created">由我指派的</el-radio-button>
            </el-radio-group>
          </el-col>
          <el-col :span="2">
            <el-select v-model="filterForm.project" placeholder="按项目搜索" clearable style="width: 100%">
              <el-option label="项目A" value="项目A" />
              <el-option label="项目B" value="项目B" />
              <el-option label="项目C" value="项目C" />
            </el-select>
          </el-col>
          <el-col :span="3">
            <el-input v-model="filterForm.taskName" placeholder="搜索任务名称" clearable/>
          </el-col>
          <el-col :span="2">
            <el-select v-model="filterForm.assignee" placeholder="执行人" clearable style="width: 100%">
              <el-option label="张三" value="张三"/>
              <el-option label="李四" value="李四"/>
              <el-option label="王五" value="王五"/>
            </el-select>
          </el-col>

          <el-col :span="5">
            <el-date-picker v-model="filterForm.deadline" type="daterange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" placeholder="预计完成时间" clearable
                            style="width: 100%"
            />
          </el-col>
          <el-col :span="3">
            <el-select v-model="filterForm.status" placeholder="任务状态" clearable style="width: 100%">
              <el-option label="待处理" value="pending"/>
              <el-option label="进行中" value="processing"/>
              <el-option label="已完成" value="completed"/>
            </el-select>
          </el-col>
          <el-col :span="3" class="filter-actions">
            <el-button type="primary" @click="handleFilter">搜索</el-button>
            <el-button icon="el-icon-download" @click="handleExport">导出</el-button>
          </el-col>
        </el-row>

      </div>
      <!-- 表格视图 -->
      <el-table v-if="viewMode === 'table'" v-loading="loading" :data="taskList" border row-key="id" :tree-props="{ children: 'children' }" default-expand-all>
        <el-table-column prop="taskName" label="任务名称" min-width="200">
          <template slot-scope="scope">
            <span class="task-name-cell">
              <i v-if="taskList.includes(scope.row)" class="el-icon-folder-opened" style="color: #409EFF; margin-right: 6px;"></i>
              <i v-else class="el-icon-document" style="color: #67C23A; margin-right: 6px;"></i>
              <span :style="{ fontWeight: taskList.includes(scope.row) ? 'bold' : 'normal' }">{{ scope.row.taskName }}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="发布人" width="100"/>
        <el-table-column prop="assignee" label="执行人" width="80"/>
        <el-table-column prop="assignee" label="审核人" width="80"/>
        <el-table-column prop="assignee" label="任务类型" width="80"/>
        <el-table-column prop="assignee" label="开始时间" width="120"/>
        <el-table-column prop="assignee" label="结束时间" width="120"/>
        <el-table-column prop="priority" label="任务级别" width="100">
          <template slot-scope="scope">
            <el-tag :type="getPriorityType(scope.row.priority)">{{ scope.row.priority }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="150">
          <template slot-scope="scope">
            <el-progress :percentage="scope.row.progress"/>
          </template>
        </el-table-column>
        <el-table-column prop="deadline" label="评分" width="120"/>
        <el-table-column prop="deadline" label="完成时间" width="120"/>
        <el-table-column label="操作" width="220">
          <template slot-scope="scope">
            <el-button size="mini" type="success" @click="handleView(scope.row)">查看</el-button>
            <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 空状态 -->
      <el-empty v-if="!loading && taskList.length === 0" description="暂无任务数据">
        <el-button type="primary" @click="handleAddTask">创建第一个任务</el-button>
      </el-empty>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50]"
          :page-size="pagination.size"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>


    <!-- 新建/编辑任务对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form ref="taskForm" :model="taskForm" :rules="taskRules" label-width="100px">
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="taskForm.taskName" placeholder="请输入任务名称" :disabled="dialogTitle === '任务详情'"/>
        </el-form-item>
        <el-form-item label="执行人" prop="assignee">
          <el-select v-model="taskForm.assignee" placeholder="请选择执行人" style="width: 100%"
                     :disabled="dialogTitle === '任务详情'"
          >
            <el-option label="张三" value="张三"/>
            <el-option label="李四" value="李四"/>
            <el-option label="王五" value="王五"/>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="taskForm.priority" placeholder="请选择优先级" style="width: 100%"
                     :disabled="dialogTitle === '任务详情'"
          >
            <el-option label="高" value="高"/>
            <el-option label="中" value="中"/>
            <el-option label="低" value="低"/>
          </el-select>
        </el-form-item>
        <el-form-item label="截止日期" prop="deadline">
          <el-date-picker
            v-model="taskForm.deadline"
            type="date"
            placeholder="选择截止日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 100%"
            :disabled="dialogTitle === '任务详情'"
          />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入任务描述"
            :disabled="dialogTitle === '任务详情'"
          />
        </el-form-item>
        <el-form-item v-if="dialogTitle === '任务详情'" label="任务状态">
          <el-tag :type="getStatusType(taskForm.status)">{{ taskForm.status }}</el-tag>
        </el-form-item>
        <el-form-item v-if="dialogTitle === '任务详情'" label="完成进度">
          <el-progress :percentage="taskForm.progress"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ dialogTitle === '任务详情' ? '关闭' : '取消' }}</el-button>
        <el-button v-if="dialogTitle !== '任务详情'" type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TaskManagement',
  data() {
    return {
      filterForm: {
        taskType: 'all',
        taskName: '',
        status: '',
        priority: '',
        assignee: '',
        deadline: [],
        project: ''
      },
      stats: {
        pending: 12,
        processing: 8,
        completed: 25,
        total: 45
      },
      taskList: [],
      loading: false,
      viewMode: 'table',
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      dialogVisible: false,
      dialogTitle: '',
      taskForm: {
        id: null,
        taskName: '',
        assignee: '',
        priority: '',
        deadline: '',
        description: ''
      },
      taskRules: {
        taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        assignee: [{ required: true, message: '请选择执行人', trigger: 'change' }],
        priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
        deadline: [{ required: true, message: '请选择截止日期', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getTaskList()
  },
  methods: {
    getTaskList() {
      this.loading = true
      setTimeout(() => {
        // 模拟数据（含二级任务）
        const allTasks = [
          {
            id: 1,
            taskName: '完成用户界面设计',
            assignee: '张三',
            creator: '李四',
            priority: '高',
            status: '进行中',
            progress: 75,
            deadline: '2024-01-15',
            description: '设计用户界面的主要页面和交互流程',
            project: '项目A',
            children: [
              {
                id: 101,
                taskName: '设计登录页',
                assignee: '张三',
                creator: '李四',
                priority: '中',
                status: '进行中',
                progress: 60,
                deadline: '2024-01-12',
                description: '设计登录页面UI'
              },
              {
                id: 102,
                taskName: '设计首页',
                assignee: '李四',
                creator: '张三',
                priority: '高',
                status: '待处理',
                progress: 0,
                deadline: '2024-01-13',
                description: '设计首页UI'
              }
            ]
          },
          {
            id: 2,
            taskName: '数据库优化',
            assignee: '王五',
            creator: '张三',
            priority: '中',
            status: '待处理',
            progress: 0,
            deadline: '2024-01-20',
            description: '优化数据库查询性能，添加索引',
            project: '项目B'
          },
          {
            id: 3,
            taskName: 'API接口开发',
            assignee: '李四',
            creator: '王五',
            priority: '高',
            status: '已完成',
            progress: 100,
            deadline: '2024-01-10',
            description: '开发RESTful API接口',
            project: '项目A',
            children: [
              {
                id: 103,
                taskName: '用户API开发',
                assignee: '李四',
                creator: '王五',
                priority: '高',
                status: '已完成',
                progress: 100,
                deadline: '2024-01-09',
                description: '开发用户相关API'
              }
            ]
          },
          {
            id: 4,
            taskName: '单元测试编写',
            assignee: '张三',
            creator: '李四',
            priority: '低',
            status: '进行中',
            progress: 30,
            deadline: '2024-01-25',
            description: '为核心功能编写单元测试',
            project: '项目C'
          },
          {
            id: 5,
            taskName: '文档编写',
            assignee: '王五',
            creator: '张三',
            priority: '中',
            status: '待处理',
            progress: 0,
            deadline: '2024-01-30',
            description: '编写项目技术文档和用户手册',
            project: '项目B'
          },
          {
            id: 6,
            taskName: '性能测试',
            assignee: '李四',
            creator: '王五',
            priority: '高',
            status: '已完成',
            progress: 100,
            deadline: '2024-01-08',
            description: '进行系统性能测试和优化',
            project: '项目A'
          }
        ]

        // 根据筛选条件过滤数据
        let filteredTasks = allTasks

        // 任务类型筛选
        if (this.filterForm.taskType === 'assigned') {
          filteredTasks = allTasks.filter(task => task.assignee === '张三') // 假设当前用户是张三
        } else if (this.filterForm.taskType === 'created') {
          filteredTasks = allTasks.filter(task => task.creator === '张三') // 假设当前用户是张三
        }

        // 任务名称搜索
        if (this.filterForm.taskName) {
          filteredTasks = filteredTasks.filter(task =>
            task.taskName.toLowerCase().includes(this.filterForm.taskName.toLowerCase())
          )
        }

        // 状态筛选
        if (this.filterForm.status) {
          const statusMap = {
            'pending': '待处理',
            'processing': '进行中',
            'completed': '已完成'
          }
          filteredTasks = filteredTasks.filter(task => task.status === statusMap[this.filterForm.status])
        }

        // 优先级筛选
        if (this.filterForm.priority) {
          filteredTasks = filteredTasks.filter(task => task.priority === this.filterForm.priority)
        }

        // 执行人筛选
        if (this.filterForm.assignee) {
          filteredTasks = filteredTasks.filter(task => task.assignee === this.filterForm.assignee)
        }
        // 预计完成时间筛选（范围）
        if (this.filterForm.deadline && this.filterForm.deadline.length === 2) {
          const [start, end] = this.filterForm.deadline
          filteredTasks = filteredTasks.filter(task => {
            return task.deadline >= start && task.deadline <= end
          })
        }

        // 按项目筛选
        if (this.filterForm.project) {
          filteredTasks = filteredTasks.filter(task => task.project === this.filterForm.project)
        }

        this.taskList = filteredTasks
        this.pagination.total = filteredTasks.length

        // 更新统计数据
        this.updateStats(allTasks)

        this.loading = false
      }, 1000)
    },
    updateStats(allTasks) {
      this.stats.pending = allTasks.filter(task => task.status === '待处理').length
      this.stats.processing = allTasks.filter(task => task.status === '进行中').length
      this.stats.completed = allTasks.filter(task => task.status === '已完成').length
      this.stats.total = allTasks.length
    },
    handleFilter() {
      this.pagination.page = 1
      this.getTaskList()
    },
    handleReset() {
      this.filterForm = {
        taskType: 'all',
        taskName: '',
        status: '',
        priority: '',
        assignee: '',
        deadline: [],
        project: ''
      }
      this.handleFilter()
    },
    handleAddTask() {
      this.dialogTitle = '新建任务'
      this.taskForm = {
        id: null,
        taskName: '',
        assignee: '',
        priority: '',
        deadline: '',
        description: ''
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '编辑任务'
      this.taskForm = { ...row }
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除这个任务吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.getTaskList()
      })
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.getTaskList()
    },
    handleCurrentChange(page) {
      this.pagination.page = page
      this.getTaskList()
    },
    getPriorityType(priority) {
      const types = { '高': 'danger', '中': 'warning', '低': 'info' }
      return types[priority] || 'info'
    },
    getStatusType(status) {
      const types = { '待处理': 'info', '进行中': 'warning', '已完成': 'success' }
      return types[status] || 'info'
    },
    handleSubmit() {
      this.$refs.taskForm.validate(valid => {
        if (valid) {
          this.$message.success('提交成功')
          this.dialogVisible = false
          this.getTaskList()
        } else {
          this.$message.error('请检查输入')
          return false
        }
      })
    },
    handleExport() {
      this.$message.success('导出功能待实现')
      // 实际导出逻辑，例如调用后端API或生成Excel文件
    },
    handleRefresh() {
      this.getTaskList()
    },
    handleView(row) {
      this.dialogTitle = '任务详情'
      this.taskForm = { ...row }
      this.dialogVisible = true
    },
    handleBatchAdd() {
      this.$message.info('批量新增功能待实现')
    },
    handleDropdownCommand(command) {
      if (command === 'batchAdd') {
        this.handleBatchAdd()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}


.stats-row {
  margin-bottom: 20px;

  .stats-card {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .stats-content {
      display: flex;
      align-items: center;

      .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 24px;
        color: white;

        &.pending {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.processing {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.completed {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.total {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }

      .stats-info {
        .stats-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
        }

        .stats-label {
          font-size: 14px;
          color: #909399;
          margin-top: 5px;
        }
      }
    }
  }
}

.filter-card {
  margin-bottom: 20px;

  .filter-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }

  .filter-label {
    margin-right: 10px;
    font-weight: 500;
    color: #606266;
  }

  .filter-actions {
    display: flex;
    gap: 10px;
  }
}

.el-card {
  .el-card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-actions {
      display: flex;
      gap: 10px;
    }
  }
}

.table-actions {
  margin-bottom: 16px;
  text-align: right;
}


.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.subtask-list {
  margin-top: 12px;
  .subtask-card {
    margin-bottom: 8px;
    margin-left: 18px;
    background: #f8fafd;
    border-left: 3px solid #409EFF;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
  }

  .stats-row {
    .el-col {
      margin-bottom: 15px;
    }
  }

  .card-view {
    .el-col {
      width: 100% !important;
    }
  }
}
::v-deep .el-table .el-table__cell {
  vertical-align: middle;
}
::v-deep .task-name-cell {
  display: flex;
  align-items: center;
}
::v-deep .el-table__indent {
  width: 32px !important;
}
</style>
