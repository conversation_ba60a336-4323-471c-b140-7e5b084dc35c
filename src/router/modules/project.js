/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const project = {
  path: '/project', component: Layout, redirect: '/project/project', name: 'project', meta: {
    title: '项目管理', icon: 'project-manage'
  }, children: [{
    path: 'projectList',
    name: 'projectList',
    component: () => import('@/views/project/project/index'),
    meta: { title: '项目管理', icon: 'project-manage' }
  }, {
    path: 'task', name: 'task',
    component: () => import('@/views/project/task/index'), meta: { title: '任务管理', icon: 'task-manage' }
  }, {
    path: 'dataTask',
    name: 'dataTask',
    component: () => import('@/views/project/dataTask/index'),
    meta: { title: '任务视图', icon: 'task-view' }
  }]
}

export default project
