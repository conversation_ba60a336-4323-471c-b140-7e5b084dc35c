<template>
  <div>
    <!-- 触发按钮 -->
    <slot name="trigger" :showPicker="showProjectPicker">
      <el-button @click="showProjectPicker">选择项目</el-button>
    </slot>

    <!-- 项目选择弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="project-picker">
        <el-row :gutter="20" style="height: 400px;">
          <!-- 左栏：项目类别 -->
          <el-col :span="8">
            <div class="category-panel">
              <div class="panel-header">项目类别</div>
              <div class="category-list">
                <div
                  v-for="category in proTypeList"
                  :key="category.id"
                  :class="['category-item', { active: selectedCategoryId === category.id }]"
                  @click="selectCategory(category)"
                >
                  {{ category.name }}
                </div>
              </div>
            </div>
          </el-col>

          <!-- 右栏：项目列表 -->
          <el-col :span="16">
            <div class="project-panel">
              <div class="panel-header">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input
                      v-model="searchForm.pro_name"
                      placeholder="搜索项目名称"
                      size="small"
                      clearable
                      @input="handleSearch"
                      prefix-icon="el-icon-search"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-select
                      v-model="searchForm.pro_managers"
                      placeholder="选择项目经理"
                      size="small"
                      clearable
                      @change="handleSearch"
                    >
                      <el-option
                        v-for="manager in projectManagers"
                        :key="manager.id"
                        :label="manager.user_username"
                        :value="manager.id"
                      />
                    </el-select>
                  </el-col>
                </el-row>
              </div>

              <div class="project-list" v-loading="loading">
                <el-checkbox-group v-model="selectedProjects" v-if="multiple">
                  <div
                    v-for="project in filteredProjects"
                    :key="project.id"
                    class="project-item"
                  >
                    <el-checkbox :label="project.id">
                      <div class="project-info">
                        <div class="project-name">{{ project.pro_name }}</div>
                        <div class="project-meta">
                          经理：{{ project.pro_managers_name }} | 类型：{{ project.pro_type_name }}
                        </div>
                      </div>
                    </el-checkbox>
                  </div>
                </el-checkbox-group>

                <div v-else>
                  <div
                    v-for="project in filteredProjects"
                    :key="project.id"
                    :class="['project-item', { selected: selectedProject === project.id }]"
                    @click="selectProject(project)"
                  >
                    <div class="project-info">
                      <div class="project-name">{{ project.pro_name }}</div>
                      <div class="project-meta">
                        经理：{{ project.pro_managers_name }} | 类型：{{ project.pro_type_name }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelect">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getproList, ProCategory, getCompanyUsers } from '@/api/porject/project'

export default {
  name: 'SeceltProduct',
  props: {
    title: {
      type: String,
      default: '选择项目'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    value: {
      type: [Object, Array],
      default: () => null
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      proTypeList: [],
      projectList: [],
      projectManagers: [],
      selectedCategoryId: null,
      selectedProject: null,
      selectedProjects: [],
      searchForm: {
        pro_name: '',
        pro_managers: ''
      }
    }
  },
  computed: {
    filteredProjects() {
      let projects = this.projectList

      // 按类别筛选
      if (this.selectedCategoryId) {
        projects = projects.filter(p => p.pro_type_id === this.selectedCategoryId)
      }

      // 按搜索条件筛选
      if (this.searchForm.pro_name) {
        projects = projects.filter(p =>
          p.pro_name.toLowerCase().includes(this.searchForm.pro_name.toLowerCase())
        )
      }

      if (this.searchForm.pro_managers) {
        projects = projects.filter(p => p.pro_managers === this.searchForm.pro_managers)
      }

      return projects
    }
  },
  methods: {
    showProjectPicker() {
      this.dialogVisible = true
      this.init()
    },

    async init() {
      this.loading = true
      try {
        await Promise.all([
          this.getProjectTypes(),
          this.getProjects(),
          this.getProjectManagers()
        ])
        this.initSelectedValues()
      } finally {
        this.loading = false
      }
    },

    initSelectedValues() {
      if (this.multiple && Array.isArray(this.value)) {
        this.selectedProjects = this.value.map(p => p.id)
      } else if (!this.multiple && this.value) {
        this.selectedProject = this.value.id
      }
    },

    async getProjectTypes() {
      const response = await ProCategory()
      this.proTypeList = response.data
    },

    async getProjects() {
      const response = await getproList({ page: 1, page_size: 1000 })
      this.projectList = response.data.data
    },

    async getProjectManagers() {
      const response = await getCompanyUsers()
      this.projectManagers = response.data
    },

    selectCategory(category) {
      this.selectedCategoryId = this.selectedCategoryId === category.id ? null : category.id
    },

    selectProject(project) {
      if (!this.multiple) {
        this.selectedProject = project.id
      }
    },

    handleSearch() {
      // 搜索逻辑已在computed中处理
    },

    confirmSelect() {
      if (this.multiple) {
        const selectedProjectData = this.projectList.filter(p =>
          this.selectedProjects.includes(p.id)
        )
        this.$emit('input', selectedProjectData)
        this.$emit('select', selectedProjectData)
      } else {
        const selectedProjectData = this.projectList.find(p => p.id === this.selectedProject)
        this.$emit('input', selectedProjectData)
        this.$emit('select', selectedProjectData)
      }
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.project-picker {
  height: 400px;
}

.category-panel, .project-panel {
  height: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.panel-header {
  padding: 10px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: bold;
}

.category-list {
  height: calc(100% - 41px);
  overflow-y: auto;
}

.category-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.category-item:hover {
  background: #f5f7fa;
}

.category-item.active {
  background: #409eff;
  color: white;
}

.project-list {
  height: calc(100% - 81px);
  overflow-y: auto;
  padding: 10px;
}

.project-item {
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.project-item:hover {
  border-color: #409eff;
}

.project-item.selected {
  border-color: #409eff;
  background: #ecf5ff;
}

.project-info {
  display: flex;
  flex-direction: column;
}

.project-name {
  font-weight: bold;
  margin-bottom: 4px;
  color: #303133;
}

.project-meta {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}
</style>
