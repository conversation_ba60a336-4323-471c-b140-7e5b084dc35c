
<!--3、新建任务时，左边总显示超期提示，确认下填写逻辑和超期判定问题。-->
<!--4、没有在任务里的人，无法写任务，可否有申请权限的功能。项目负责人收到信息可以操作（可以避免单独微信沟通加人这个事情）-->
<!--7、刷新同步问题，有时候别人写完，后台需要刷新多次才可以刷到最新数据，看能否有实时同步功能？


<!--5、任务视图，头两行是否可以冻结，这样往下刷信息，看不到是哪天的任务，信息和日期容易对不上。-->
<!--6、任务视图，每次打开默认直接展示本周的周一到周日的，不用每次都自己再去操作选日期了（自己自定义选择日期功能保留）-->



##a`                                                                           锦冠数字化OA管理平台

安装依赖包

​			npm install

项目启动

<!--3、新建任务时，左边总显示超期提示，确认下填写逻辑和超期判定问题。-->
<!--4、没有在任务里的人，无法写任务，可否有申请权限的功能。项目负责人收到信息可以操作（可以避免单独微信沟通加人这个事情）-->
<!--7、刷新同步问题，有时候别人写完，后台需要刷新多次才可以刷到最新数据，看能否有实时同步功能？

项目打包
​			npm  run build:prod
打测试包 
​		    npm run build:stage

<!--5、任务视图，头两行是否可以冻结，这样往下刷信息，看不到是哪天的任务，信息和日期容易对不上。-->
<!--6、任务视图，每次打开默认直接展示本周的周一到周日的，不用每次都自己再去操作选日期了（自己自定义选择日期功能保留）-->


vue.config.js     --文件内选择连接接口地址
.env.production   --确定打包后连接的地址
.env.staging      -- 测试包接口地址
 npm i xlsx
 npm i xlsx-style
在xlsx-style下dist里面cpexcel.js的807行
在\node_modules\xlsx-style\dist\cpexcel.js  
// var cpt = require('./cpt' + 'able');
修改为
var cpt = cptable;
